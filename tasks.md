# Current Task: Lifetime Insights card tweaks (percentages, zero styles, grid + divider)

## Context7 + Sequential Thinking
- Context: `components/dashboard/dashboard.js` renders the dashboard via a template string (`dashboardHTML`) and uses classes like `sales-analytics-div` and `marketplaces-sales-row`. Styles live in `snapapp.css` with sections for `.four-sales-cards-section` and `.top-four-sales-cards-section`.
- Goal: Add two cards side by side under `top-four-sales-cards-section`:
  - Lifetime Insights: same structure as top-four cards (same `sales-analytics-div`, same `marketplaces-sales-row`), plus a new `lifetime-data-div` at the bottom.
  - Customer Feedback & Engagement: different layout per Figma; include reviews count, comments, average rating, marketplaces summary, and a small reviews list.
- Equal height requirement: Both cards must visually match in height.

## Implementation Tasks
- [x] Show percentage next to `metric-col ads-metric` in `lifetime-insights-card-div` similar to Returned.
- [x] Apply zero color style to `(0)` for `.marketplace-total-returned-units` instances in Lifetime card.
- [x] Make `lifetime-data-div` a 3-column x 2-row grid and ensure it uses 100% card width.
- [ ] Ensure `sales-section-divider` inside `lifetime-insights-card-div` touches both left and right edges exactly like in `top-month-card-div`.

### Global Style Update
- [x] Change dark theme `--bg-primary` to `#1A1D23` in global styles (`snapapp.css`).
 - [x] Update all dark-mode loader overlays to match new primary: rgba(26,29,35,0.95/0.9) in `snapapp.css` and `components/charts/snap-charts.css`.

## Testing Strategy
- Open `index.html` and scroll below `top-four-sales-cards-section`.
- Verify Ads shows a percentage next to its label, styled like `.ads-percentage`.
- Confirm all zero `(0)` returned unit values are grey using the `.zero` style.
- Confirm `lifetime-data-div` renders as 3 columns, 2 rows, and spans the full card width.
- Check the divider spans edge-to-edge within the Lifetime card.

## Notes
- Data is static/dummy per current dashboard patterns. No Chrome APIs or storage changes.
